// This is a generated file. Do not edit directly.

module k8s.io/kube-scheduler

go 1.24.0

godebug default=go1.24

require (
	github.com/google/go-cmp v0.7.0
	k8s.io/api v0.0.0-20250718010530-b3927ff69476
	k8s.io/apimachinery v0.0.0-20250717210244-b92abb2d8139
	k8s.io/component-base v0.0.0-20250717172125-4e07767df717
	k8s.io/klog/v2 v2.130.1
	sigs.k8s.io/yaml v1.5.0
)

require (
	github.com/fxamacker/cbor/v2 v2.8.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.3-0.20250322232337-35a7c28c31ee // indirect
	github.com/x448/float16 v0.8.4 // indirect
	go.yaml.in/yaml/v2 v2.4.2 // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	k8s.io/utils v0.0.0-20250604170112-4c0f3b243397 // indirect
	sigs.k8s.io/json v0.0.0-20241014173422-cfa47c3a1cc8 // indirect
	sigs.k8s.io/randfill v1.0.0 // indirect
	sigs.k8s.io/structured-merge-diff/v6 v6.3.0 // indirect
)

replace k8s.io/client-go => k8s.io/client-go v0.0.0-20250718010928-be36413bbca7
